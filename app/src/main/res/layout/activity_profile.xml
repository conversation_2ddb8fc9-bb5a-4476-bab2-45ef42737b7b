<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?android:colorBackground">

    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/appBarLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/primary"
        app:elevation="4dp">

        <com.google.android.material.appbar.MaterialToolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            style="@style/Widget.CareerWorx.Toolbar"
            app:titleTextAppearance="@style/TextAppearance.MaterialComponents.Headline6" />

    </com.google.android.material.appbar.AppBarLayout>

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">


            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="24dp"
                app:cardCornerRadius="16dp"
                app:cardElevation="2dp"
                app:strokeWidth="1dp"
                app:strokeColor="#E0E0E0">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:orientation="vertical"
                    android:padding="24dp">

                    <com.google.android.material.imageview.ShapeableImageView
                        android:id="@+id/profileImage"
                        android:layout_width="150dp"
                        android:layout_height="150dp"
                        android:scaleType="centerCrop"
                        android:src="@android:drawable/ic_menu_myplaces"
                        app:shapeAppearanceOverlay="@style/CircleImageView" />

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/changePhotoButton"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="16dp"
                        android:text="Change Photo"
                        style="@style/Widget.MaterialComponents.Button.TextButton"
                        android:textColor="?android:attr/textColorPrimary" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>


            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="24dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="2dp"
                app:strokeWidth="1dp"
                app:strokeColor="#E0E0E0">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="24dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Personal Information"
                        android:textSize="20sp"
                        android:textStyle="bold"
                        android:textColor="?android:attr/textColorPrimary"
                        android:layout_marginBottom="24dp"/>

                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="16dp"
                        style="@style/Widget.CareerWorx.TextInputLayout">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/nameInput"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:hint="Name"
                            android:textColor="?android:attr/textColorPrimary"
                            android:inputType="textPersonName"/>
                    </com.google.android.material.textfield.TextInputLayout>

                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="16dp"
                        style="@style/Widget.CareerWorx.TextInputLayout">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/surnameInput"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:hint="Surname"
                            android:textColor="?android:attr/textColorPrimary"
                            android:inputType="textPersonName"/>
                    </com.google.android.material.textfield.TextInputLayout>

                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="16dp"
                        style="@style/Widget.CareerWorx.TextInputLayout">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/emailInput"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:hint="Email"
                            android:textColor="?android:attr/textColorPrimary"
                            android:inputType="textEmailAddress"/>
                    </com.google.android.material.textfield.TextInputLayout>

                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="16dp"
                        style="@style/Widget.CareerWorx.TextInputLayout">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/phoneNumberInput"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:hint="Phone Number"
                            android:textColor="?android:attr/textColorPrimary"
                            android:inputType="phone"/>
                    </com.google.android.material.textfield.TextInputLayout>

                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/provinceLayout"
                        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:hint="Province">

                        <AutoCompleteTextView
                            android:id="@+id/provinceInput"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="none" />

                    </com.google.android.material.textfield.TextInputLayout>

                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="8dp"
                        style="@style/Widget.CareerWorx.TextInputLayout">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/addressInput"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:hint="Address" />
                    </com.google.android.material.textfield.TextInputLayout>



                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>


            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="24dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="2dp"
                app:strokeWidth="1dp"
                app:strokeColor="#E0E0E0">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="24dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Professional Summary"
                        android:textSize="20sp"
                        android:textStyle="bold"
                        android:textColor="?android:attr/textColorPrimary"
                        android:layout_marginBottom="16dp"/>

                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/yearsOfExperienceLayout"
                        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="16dp"
                        android:hint="Years of Experience">

                        <AutoCompleteTextView
                            android:id="@+id/yearsOfExperienceInput"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="none" />

                    </com.google.android.material.textfield.TextInputLayout>



                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/expectedSalaryLayout"
                        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="16dp"
                        android:hint="Expected Salary">

                        <AutoCompleteTextView
                            android:id="@+id/expectedSalaryInput"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="none" />

                    </com.google.android.material.textfield.TextInputLayout>

                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/fieldLayout"
                        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="16dp"
                        android:hint="Field">

                        <AutoCompleteTextView
                            android:id="@+id/fieldInput"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="none" />

                    </com.google.android.material.textfield.TextInputLayout>

                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/subFieldLayout"
                        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="16dp"
                        android:hint="Specialization">

                        <AutoCompleteTextView
                            android:id="@+id/subFieldInput"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="none" />

                    </com.google.android.material.textfield.TextInputLayout>

                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        style="@style/Widget.CareerWorx.TextInputLayout">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/summaryInput"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:hint="Write a brief summary about yourself"
                            android:textColor="?android:attr/textColorPrimary"
                            android:inputType="textMultiLine"
                            android:minLines="3"
                            android:gravity="top"/>
                    </com.google.android.material.textfield.TextInputLayout>

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/getSuggestionsButton"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Get Summary Suggestions"
                        style="@style/Widget.MaterialComponents.Button.TextButton"
                        android:textColor="?android:attr/textColorPrimary"
                        android:layout_gravity="end"/>



                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>


            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="24dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="2dp"
                app:strokeWidth="1dp"
                app:strokeColor="#E0E0E0">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="24dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Skills"
                        android:textSize="20sp"
                        android:textStyle="bold"
                        android:textColor="?android:attr/textColorPrimary"
                        android:layout_marginBottom="16dp"/>

                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="16dp"
                        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu"
                        app:boxStrokeColor="?attr/colorPrimary">

                        <AutoCompleteTextView
                            android:id="@+id/skillsInput"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:hint="Select skills"
                            android:inputType="none"
                            android:textColor="?android:attr/textColorPrimary"/>
                    </com.google.android.material.textfield.TextInputLayout>

                    <com.google.android.material.chip.ChipGroup
                        android:id="@+id/skillsChipGroup"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"/>

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>


            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="24dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="2dp"
                app:strokeWidth="1dp"
                app:strokeColor="#E0E0E0">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="24dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Work Experience"
                        android:textSize="20sp"
                        android:textStyle="bold"
                        android:textColor="?android:attr/textColorPrimary"
                        android:layout_marginBottom="16dp"/>

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/experienceRecyclerView"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="16dp"/>

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/addExperienceButton"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Add Experience"
                        style="@style/Widget.MaterialComponents.Button.TextButton"
                        android:textColor="?android:attr/textColorPrimary"/>

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>




            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="24dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="2dp"
                app:strokeWidth="1dp"
                app:strokeColor="#E0E0E0">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="24dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Education"
                        android:textSize="20sp"
                        android:textStyle="bold"
                        android:textColor="?android:attr/textColorPrimary"
                        android:layout_marginBottom="16dp"/>

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/educationRecyclerView"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="16dp"/>

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/addEducationButton"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Add Education"
                        style="@style/Widget.MaterialComponents.Button.TextButton"
                        android:textColor="?android:attr/textColorPrimary"/>

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Certificates Section -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="24dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="2dp"
                app:strokeWidth="1dp"
                app:strokeColor="#E0E0E0">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="24dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Certificates & Achievements"
                        android:textSize="20sp"
                        android:textStyle="bold"
                        android:textColor="?android:attr/textColorPrimary"
                        android:layout_marginBottom="16dp"/>

                    <!-- Certificate Badges (Visual Display) -->
                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Certificate Badges"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        android:textColor="?android:attr/textColorPrimary"
                        android:layout_marginBottom="8dp"/>

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/certificateBadgesRecyclerView"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="16dp"/>

                    <!-- Certificate Management (Form-based) -->
                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Manage Certificates"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        android:textColor="?android:attr/textColorPrimary"
                        android:layout_marginBottom="8dp"/>

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/certificatesRecyclerView"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="16dp"/>

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/addCertificateButton"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Add Certificate"
                        style="@style/Widget.MaterialComponents.Button.TextButton"
                        android:textColor="?android:attr/textColorPrimary"/>

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="24dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="2dp"
                app:strokeWidth="1dp"
                app:strokeColor="#E0E0E0">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="24dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Social Links"
                        android:textSize="20sp"
                        android:textStyle="bold"
                        android:textColor="?android:attr/textColorPrimary"
                        android:layout_marginBottom="16dp"/>

                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="16dp"
                        style="@style/Widget.CareerWorx.TextInputLayout">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/linkedinInput"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:hint="LinkedIn Profile"
                            android:textColor="?android:attr/textColorPrimary"
                            android:inputType="textUri"/>
                    </com.google.android.material.textfield.TextInputLayout>

                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="16dp"
                        style="@style/Widget.CareerWorx.TextInputLayout">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/githubInput"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:hint="GitHub Profile"
                            android:textColor="?android:attr/textColorPrimary"
                            android:inputType="textUri"/>
                    </com.google.android.material.textfield.TextInputLayout>

                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        style="@style/Widget.CareerWorx.TextInputLayout">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/portfolioInput"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:hint="Portfolio Website"
                            android:textColor="?android:attr/textColorPrimary"
                            android:inputType="textUri"/>
                    </com.google.android.material.textfield.TextInputLayout>

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>


            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="24dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="2dp"
                app:strokeWidth="1dp"
                app:strokeColor="#E0E0E0">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="24dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="References"
                        android:textSize="20sp"
                        android:textStyle="bold"
                        android:textColor="?android:attr/textColorPrimary"
                        android:layout_marginBottom="16dp"/>

                    <LinearLayout
                        android:id="@+id/referencesContainer"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:layout_marginBottom="16dp"/>

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/addReferenceButton"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Add Reference"
                        style="@style/Widget.MaterialComponents.Button.TextButton"
                        android:textColor="?android:attr/textColorPrimary"/>

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>


            <com.google.android.material.button.MaterialButton
                android:id="@+id/saveButton"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Save Profile"
                android:textColor="@color/white"
                android:padding="12dp"
                android:layout_marginTop="16dp"
                android:layout_marginBottom="32dp"
                app:cornerRadius="8dp"/>

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

</androidx.coordinatorlayout.widget.CoordinatorLayout>