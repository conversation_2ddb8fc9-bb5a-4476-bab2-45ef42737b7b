package com.example.jobrec.ai

import android.util.Log
import com.example.jobrec.BuildConfig
import com.example.jobrec.chatbot.HuggingFaceService
import com.example.jobrec.Job
import com.example.jobrec.models.JobMatch
import com.example.jobrec.models.MatchCriteria
import com.example.jobrec.User
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

class JobMatchingService {
    private val huggingFaceService = HuggingFaceService()
    private val TAG = "JobMatchingService"

    private val huggingFaceToken: String
        get() = BuildConfig.HUGGING_FACE_TOKEN

    suspend fun calculateJobMatch(user: User, job: Job): JobMatch {
        return withContext(Dispatchers.IO) {
            try {
                val matchCriteria = extractMatchCriteria(user)
                val aiMatchResult = getAIMatchAnalysis(matchCriteria, job)
                val ruleBasedMatch = calculateRuleBasedMatch(user, job)

                val finalPercentage = (aiMatchResult.first + ruleBasedMatch) / 2
                val reasoning = aiMatchResult.second

                JobMatch(
                    job = job.copy(
                        matchPercentage = finalPercentage,
                        matchReasoning = reasoning
                    ),
                    matchPercentage = finalPercentage,
                    matchReasoning = reasoning,
                    skillsMatch = calculateSkillsMatch(user.skills, job.requirements),
                    experienceMatch = calculateExperienceMatch(user.experience, job.experienceLevel),
                    educationMatch = calculateEducationMatch(user.education, job.jobField),
                    locationMatch = calculateLocationMatch(user.province, job.province)
                )
            } catch (e: Exception) {
                Log.e(TAG, "Error calculating job match", e)
                val fallbackMatch = calculateRuleBasedMatch(user, job)
                JobMatch(
                    job = job.copy(
                        matchPercentage = fallbackMatch,
                        matchReasoning = "Match calculated using profile analysis"
                    ),
                    matchPercentage = fallbackMatch,
                    matchReasoning = "Match calculated using profile analysis"
                )
            }
        }
    }

    private fun extractMatchCriteria(user: User): MatchCriteria {
        return MatchCriteria(
            skills = user.skills,
            experience = user.experience.map { "${it.position} at ${it.company} (${it.startDate} - ${it.endDate})" },
            education = user.education.map { "${it.degree} in ${it.fieldOfStudy} from ${it.institution}" },
            summary = user.summary,
            location = user.province
        )
    }

    private suspend fun getAIMatchAnalysis(criteria: MatchCriteria, job: Job): Pair<Int, String> {
        return try {
            val prompt = buildMatchPrompt(criteria, job)
            val response = huggingFaceService.generateResponse(prompt, huggingFaceToken)
            parseAIResponse(response)
        } catch (e: Exception) {
            Log.e(TAG, "AI analysis failed, using fallback", e)
            Pair(50, "Unable to perform detailed AI analysis")
        }
    }

    private fun buildMatchPrompt(criteria: MatchCriteria, job: Job): String {
        return """
            Analyze job compatibility and provide a match percentage (0-100) and brief reasoning.

            Candidate Profile:
            - Skills: ${criteria.skills.joinToString(", ")}
            - Experience: ${criteria.experience.joinToString("; ")}
            - Education: ${criteria.education.joinToString("; ")}
            - Summary: ${criteria.summary}
            - Location: ${criteria.location}

            Job Requirements:
            - Title: ${job.title}
            - Field: ${job.jobField}
            - Specialization: ${job.specialization}
            - Experience Level: ${job.experienceLevel}
            - Requirements: ${job.requirements}
            - Location: ${job.province}

            Respond with: "MATCH: [percentage]% - [brief reasoning]"
        """.trimIndent()
    }

    private fun parseAIResponse(response: String): Pair<Int, String> {
        return try {
            val matchRegex = """MATCH:\s*(\d+)%\s*-\s*(.+)""".toRegex()
            val matchResult = matchRegex.find(response)

            if (matchResult != null) {
                val percentage = matchResult.groupValues[1].toInt().coerceIn(0, 100)
                val reasoning = matchResult.groupValues[2].trim()
                Pair(percentage, reasoning)
            } else {
                val percentageRegex = """(\d+)%""".toRegex()
                val percentageMatch = percentageRegex.find(response)
                val percentage = percentageMatch?.groupValues?.get(1)?.toInt()?.coerceIn(0, 100) ?: 50
                Pair(percentage, "AI analysis completed")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error parsing AI response", e)
            Pair(50, "Analysis completed with standard matching")
        }
    }

    private fun calculateRuleBasedMatch(user: User, job: Job): Int {
        val skillsScore = calculateSkillsMatch(user.skills, job.requirements)
        val experienceScore = calculateExperienceMatch(user.experience, job.experienceLevel)
        val educationScore = calculateEducationMatch(user.education, job.jobField)
        val locationScore = calculateLocationMatch(user.province, job.province)

        // Weighted calculation - skills are most important
        val finalScore = ((skillsScore * 0.6) + (experienceScore * 0.2) + (educationScore * 0.15) + (locationScore * 0.05)).toInt()

        return finalScore.coerceAtMost(100)
    }

    private fun calculateSkillsMatch(userSkills: List<String>, jobRequirements: String): Int {
        if (userSkills.isEmpty()) return 30 // Lower base score if no skills
        if (jobRequirements.isEmpty()) return 50 // Moderate score if no requirements

        val requirements = jobRequirements.lowercase()
        val jobTitle = extractJobTitle(requirements) // Try to get job context

        var exactMatches = 0
        var partialMatches = 0
        var relatedMatches = 0
        var totalSkills = 0

        for (skill in userSkills) {
            val skillLower = skill.lowercase().trim()
            if (skillLower.isEmpty()) continue

            totalSkills++

            when {
                // Exact skill match
                requirements.contains(skillLower) -> exactMatches++
                // Partial match (skill keywords in requirements)
                skillLower.split(" ").any { word ->
                    word.length > 2 && requirements.contains(word)
                } -> partialMatches++
                // Related technology match
                isRelatedSkill(skillLower, requirements) -> relatedMatches++
            }
        }

        // Calculate score based on match quality and relevance
        val matchScore = when {
            exactMatches > 0 -> {
                // Strong match - calculate based on exact matches
                val exactPercentage = (exactMatches.toDouble() / totalSkills * 100).toInt()
                (exactPercentage * 0.8 + 80).toInt().coerceAtMost(100) // 80-100% range
            }
            partialMatches > 0 -> {
                // Good match - calculate based on partial matches
                val partialPercentage = (partialMatches.toDouble() / totalSkills * 100).toInt()
                (partialPercentage * 0.6 + 50).toInt().coerceAtMost(85) // 50-85% range
            }
            relatedMatches > 0 -> {
                // Related match - more conservative
                val relatedPercentage = (relatedMatches.toDouble() / totalSkills * 100).toInt()
                (relatedPercentage * 0.4 + 30).toInt().coerceAtMost(70) // 30-70% range
            }
            else -> {
                // No relevant matches - check if it's a complete mismatch
                if (isCompleteMismatch(userSkills, requirements)) {
                    15 // Very low score for obvious mismatches
                } else {
                    35 // Moderate score for unclear cases
                }
            }
        }

        return matchScore
    }

    private fun extractJobTitle(requirements: String): String {
        // Try to extract job context from requirements
        val commonTitles = listOf("developer", "engineer", "designer", "manager", "analyst", "tester", "consultant")
        return commonTitles.find { requirements.contains(it) } ?: ""
    }

    private fun isCompleteMismatch(userSkills: List<String>, jobRequirements: String): Boolean {
        val userIsTechnical = userSkills.any { isTechnicalSkill(it.lowercase()) }
        val jobIsTechnical = isTechnicalJob(jobRequirements)

        // If user is clearly technical but job is clearly non-technical (or vice versa)
        return (userIsTechnical && !jobIsTechnical && !jobRequirements.contains("technical")) ||
               (!userIsTechnical && jobIsTechnical)
    }

    private fun isTechnicalSkill(skill: String): Boolean {
        val technicalSkills = listOf("programming", "coding", "development", "software", "web", "mobile", "database", "api", "framework", "library")
        return technicalSkills.any { skill.contains(it) }
    }

    private fun isTechnicalJob(requirements: String): Boolean {
        val technicalKeywords = listOf("programming", "coding", "development", "software", "web", "mobile", "database", "api", "technical")
        return technicalKeywords.any { requirements.contains(it) }
    }

    private fun isRelatedSkill(skill: String, requirements: String): Boolean {
        val relatedSkills = mapOf(
            "java" to listOf("spring", "android", "kotlin", "programming"),
            "kotlin" to listOf("java", "android", "programming"),
            "python" to listOf("django", "flask", "data", "programming"),
            "javascript" to listOf("react", "node", "web", "frontend", "backend"),
            "react" to listOf("javascript", "frontend", "web"),
            "sql" to listOf("database", "mysql", "postgresql", "data"),
            "html" to listOf("css", "web", "frontend"),
            "css" to listOf("html", "web", "frontend", "design")
        )

        return relatedSkills[skill]?.any { related ->
            requirements.contains(related)
        } ?: false
    }

    private fun calculateExperienceMatch(userExperience: List<com.example.jobrec.Experience>, jobExperienceLevel: String): Int {
        if (userExperience.isEmpty()) {
            return when {
                jobExperienceLevel.lowercase().contains("entry") ||
                jobExperienceLevel.lowercase().contains("junior") ||
                jobExperienceLevel.lowercase().contains("0-1") -> 85 // Good for entry level
                else -> 60 // Still reasonable for other levels
            }
        }

        val totalYears = userExperience.sumOf { parseExperienceYears(it.startDate, it.endDate) }
        val experienceLevelLower = jobExperienceLevel.lowercase()

        return when {
            // Entry level positions
            experienceLevelLower.contains("entry") ||
            experienceLevelLower.contains("junior") ||
            experienceLevelLower.contains("0-1") -> {
                when {
                    totalYears <= 2 -> 100
                    totalYears <= 4 -> 90
                    else -> 85 // Even senior people can apply for junior roles
                }
            }
            // Mid level positions
            experienceLevelLower.contains("mid") ||
            experienceLevelLower.contains("intermediate") ||
            experienceLevelLower.contains("2-5") -> {
                when {
                    totalYears in 2..5 -> 100
                    totalYears in 1..7 -> 90
                    totalYears < 1 -> 75 // Still possible with strong skills
                    else -> 85
                }
            }
            // Senior level positions
            experienceLevelLower.contains("senior") ||
            experienceLevelLower.contains("5+") ||
            experienceLevelLower.contains("lead") -> {
                when {
                    totalYears >= 5 -> 100
                    totalYears >= 3 -> 85
                    totalYears >= 1 -> 70
                    else -> 55
                }
            }
            // No specific level mentioned
            else -> {
                when {
                    totalYears >= 1 -> 85
                    else -> 75
                }
            }
        }
    }

    private fun parseExperienceYears(startDate: String, endDate: String): Int {
        return try {
            val startYear = startDate.substringAfterLast("/").toIntOrNull() ?:
                           startDate.substringAfterLast("-").toIntOrNull() ?:
                           java.util.Calendar.getInstance().get(java.util.Calendar.YEAR)

            val endYear = if (endDate.lowercase().contains("present") || endDate.lowercase().contains("current") || endDate.isEmpty()) {
                java.util.Calendar.getInstance().get(java.util.Calendar.YEAR)
            } else {
                endDate.substringAfterLast("/").toIntOrNull() ?:
                endDate.substringAfterLast("-").toIntOrNull() ?:
                java.util.Calendar.getInstance().get(java.util.Calendar.YEAR)
            }

            kotlin.math.max(1, endYear - startYear)
        } catch (e: Exception) {
            1
        }
    }

    private fun calculateEducationMatch(userEducation: List<com.example.jobrec.Education>, jobField: String): Int {
        if (userEducation.isEmpty()) return 40 // More realistic base score
        if (jobField.isEmpty()) return 70 // Moderate score if no field specified

        val jobFieldLower = jobField.lowercase()
        var bestMatch = 50 // Base score for having education

        for (education in userEducation) {
            val fieldOfStudy = education.fieldOfStudy.lowercase()
            val degree = education.degree.lowercase()

            val matchScore = when {
                // Exact field match
                fieldOfStudy.contains(jobFieldLower) || jobFieldLower.contains(fieldOfStudy) -> 95
                // Related field match
                isRelatedField(fieldOfStudy, jobFieldLower) -> 85
                // Technical degree for technical job
                (isTechnicalField(jobFieldLower) && isTechnicalDegree(fieldOfStudy)) -> 75
                // Non-technical degree for non-technical job
                (!isTechnicalField(jobFieldLower) && !isTechnicalDegree(fieldOfStudy)) -> 70
                // Technical degree for non-technical job (still valuable)
                (!isTechnicalField(jobFieldLower) && isTechnicalDegree(fieldOfStudy)) -> 65
                // Non-technical degree for technical job (less ideal but possible)
                (isTechnicalField(jobFieldLower) && !isTechnicalDegree(fieldOfStudy)) -> 45
                // Any degree
                degree.contains("bachelor") || degree.contains("master") || degree.contains("degree") -> 60
                // Basic education
                else -> 50
            }

            bestMatch = maxOf(bestMatch, matchScore)
        }

        return bestMatch
    }

    private fun isRelatedField(userField: String, jobField: String): Boolean {
        val relatedFields = mapOf(
            "computer science" to listOf("software", "programming", "technology", "it", "development"),
            "information technology" to listOf("software", "programming", "computer", "development"),
            "software engineering" to listOf("programming", "development", "technology", "computer"),
            "business" to listOf("management", "marketing", "finance", "administration"),
            "engineering" to listOf("technical", "development", "technology"),
            "design" to listOf("ui", "ux", "graphic", "creative", "visual")
        )

        return relatedFields.entries.any { (field, related) ->
            (userField.contains(field) && related.any { jobField.contains(it) }) ||
            (jobField.contains(field) && related.any { userField.contains(it) })
        }
    }

    private fun isTechnicalField(field: String): Boolean {
        val technicalKeywords = listOf("software", "programming", "development", "technology", "it", "computer", "engineering")
        return technicalKeywords.any { field.contains(it) }
    }

    private fun isTechnicalDegree(degree: String): Boolean {
        val technicalDegrees = listOf("computer", "software", "engineering", "technology", "information", "mathematics", "science")
        return technicalDegrees.any { degree.contains(it) }
    }

    private fun calculateLocationMatch(userLocation: String, jobLocation: String): Int {
        if (userLocation.isEmpty() || jobLocation.isEmpty()) return 70 // Moderate score for missing data

        val userLoc = userLocation.lowercase().trim()
        val jobLoc = jobLocation.lowercase().trim()

        return when {
            // Exact match
            userLoc == jobLoc -> 100
            // Remote work
            jobLoc.contains("remote") || jobLoc.contains("anywhere") || jobLoc.contains("work from home") -> 95
            // Same province/state
            userLoc.contains(jobLoc) || jobLoc.contains(userLoc) -> 85
            // Different location
            else -> 60
        }
    }
}
